"""
Enhanced webhook security service with comprehensive protection.
"""
import time
import hashlib
import hmac
import logging
from typing import Dict, Any, Optional
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from ..exceptions import WebhookSignatureError, WebhookError

logger = logging.getLogger(__name__)


class WebhookSecurityService:
    """
    Comprehensive webhook security service with:
    - Signature verification
    - Replay attack protection
    - Rate limiting
    - Security event logging
    """
    
    def __init__(self):
        self.replay_window = 300  # 5 minutes
        self.rate_limit_window = 60  # 1 minute
        self.max_requests_per_window = 100
        
    def verify_webhook_security(self, payload: bytes, signature: str, 
                               timestamp: Optional[str] = None,
                               source_ip: Optional[str] = None) -> Dict[str, Any]:
        """
        Comprehensive webhook security verification.
        
        Args:
            payload: Raw webhook payload
            signature: Stripe signature header
            timestamp: Request timestamp
            source_ip: Source IP address
            
        Returns:
            Verification result with security metadata
            
        Raises:
            WebhookSignatureError: If verification fails
            WebhookError: If security checks fail
        """
        security_context = {
            'timestamp': timestamp or str(int(time.time())),
            'source_ip': source_ip,
            'payload_hash': hashlib.sha256(payload).hexdigest(),
            'signature_valid': False,
            'replay_check_passed': False,
            'rate_limit_passed': False
        }
        
        try:
            # 1. Verify signature
            self._verify_signature(payload, signature, security_context)
            
            # 2. Check for replay attacks
            self._check_replay_attack(payload, timestamp, security_context)
            
            # 3. Rate limiting
            self._check_rate_limit(source_ip, security_context)
            
            # 4. Log security event
            self._log_security_event('webhook_verified', security_context)
            
            return security_context
            
        except Exception as e:
            # Log security failure
            security_context['error'] = str(e)
            self._log_security_event('webhook_verification_failed', security_context)
            raise
    
    def _verify_signature(self, payload: bytes, signature: str, 
                         context: Dict[str, Any]) -> None:
        """Verify webhook signature"""
        import stripe
        
        # Get correct webhook secret
        webhook_secret = self._get_webhook_secret()
        
        try:
            # Parse signature header
            elements = signature.split(',')
            signature_dict = {}
            
            for element in elements:
                key, value = element.split('=', 1)
                signature_dict[key] = value
            
            timestamp = signature_dict.get('t')
            signatures = [
                signature_dict.get('v1'),
                signature_dict.get('v0')  # Fallback for older versions
            ]
            
            # Verify timestamp (prevent replay attacks)
            if timestamp:
                request_time = int(timestamp)
                current_time = int(time.time())
                
                if abs(current_time - request_time) > self.replay_window:
                    raise WebhookSignatureError(
                        f"Request timestamp too old: {current_time - request_time}s"
                    )
            
            # Verify signature
            expected_signature = self._compute_signature(payload, timestamp, webhook_secret)
            
            signature_valid = False
            for sig in signatures:
                if sig and hmac.compare_digest(expected_signature, sig):
                    signature_valid = True
                    break
            
            if not signature_valid:
                raise WebhookSignatureError("Invalid webhook signature")
            
            context['signature_valid'] = True
            context['timestamp_verified'] = timestamp is not None
            
        except Exception as e:
            logger.error(f"Signature verification failed: {str(e)}")
            raise WebhookSignatureError(f"Signature verification failed: {str(e)}")
    
    def _compute_signature(self, payload: bytes, timestamp: str, secret: str) -> str:
        """Compute expected signature"""
        signed_payload = f"{timestamp}.{payload.decode('utf-8')}"
        return hmac.new(
            secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_webhook_secret(self) -> str:
        """Get correct webhook secret for current environment"""
        from ..config import BillingConfig
        
        stripe_keys = BillingConfig.get_stripe_keys()
        
        if getattr(settings, 'STRIPE_LIVE_MODE', False):
            secret = stripe_keys.get('live_webhook_secret')
            if not secret:
                raise WebhookError("Live webhook secret not configured")
        else:
            secret = stripe_keys.get('test_webhook_secret')
            if not secret:
                raise WebhookError("Test webhook secret not configured")
        
        return secret
    
    def _check_replay_attack(self, payload: bytes, timestamp: Optional[str], 
                           context: Dict[str, Any]) -> None:
        """Check for replay attacks using payload hash"""
        payload_hash = hashlib.sha256(payload).hexdigest()
        cache_key = f"webhook:replay:{payload_hash}"
        
        # Check if we've seen this payload before
        if cache.get(cache_key):
            raise WebhookError(f"Replay attack detected: {payload_hash}")
        
        # Store payload hash to prevent replay
        cache.set(cache_key, True, timeout=self.replay_window)
        context['replay_check_passed'] = True
    
    def _check_rate_limit(self, source_ip: Optional[str], 
                         context: Dict[str, Any]) -> None:
        """Check rate limiting per IP"""
        if not source_ip:
            context['rate_limit_passed'] = True
            return
        
        cache_key = f"webhook:rate_limit:{source_ip}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= self.max_requests_per_window:
            raise WebhookError(f"Rate limit exceeded for IP: {source_ip}")
        
        # Increment counter
        cache.set(cache_key, current_count + 1, timeout=self.rate_limit_window)
        context['rate_limit_passed'] = True
        context['request_count'] = current_count + 1
    
    def _log_security_event(self, event_type: str, context: Dict[str, Any]) -> None:
        """Log security events for monitoring"""
        log_data = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'context': context
        }
        
        if event_type == 'webhook_verification_failed':
            logger.error(f"SECURITY EVENT: {event_type}", extra=log_data)
        else:
            logger.info(f"Security event: {event_type}", extra=log_data)


# Global instance
webhook_security = WebhookSecurityService()
